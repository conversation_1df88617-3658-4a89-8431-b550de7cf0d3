-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS uniconnect_db;

-- Use the database
USE uniconnect_db;

-- Create the users table
CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    department VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Display the table structure
DESCRIBE users;

-- Optional: Create a test user (password is 'password' hashed with SHA-256)
-- INSERT INTO users (full_name, student_id, email, password, department)
-- VALUES ('Test User', '12345678', '<EMAIL>', '5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8', 'CSE');

-- Optional: View all users
-- SELECT * FROM users;
