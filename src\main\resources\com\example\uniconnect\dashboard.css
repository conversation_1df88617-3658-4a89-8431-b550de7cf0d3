/* Global Styles - matching DBMS project exactly */
.root {
    -fx-font-family: "Arial", "sans-serif";
    -fx-font-size: 14px;
    -fx-background-color: #F5F5F5;
}

/* Sidebar Styles */
.sidebar {
    -fx-background-color: #1A237E;
    -fx-pref-width: 200px;
    -fx-spacing: 10;
    -fx-padding: 10;
}

.sidebar .active {
    -fx-background-color: #0D47A1;
}

.sidebar .sidebar-item {
    -fx-padding: 10;
    -fx-text-fill: #FFFFFF;
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-cursor: hand;
}

.sidebar .sidebar-item:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
}

/* Header Styles */
.header {
    -fx-background-color: #E0E0E0;
    -fx-padding: 10;
    -fx-spacing: 10;
}

.greeting {
    -fx-font-weight: bold;
    -fx-font-size: 18px;
}

.notification {
    -fx-spacing: 10;
    -fx-alignment: center-right;
}

.nav-icon {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 20px;
    -fx-cursor: hand;
    -fx-padding: 5;
}

.profile-img {
    -fx-background-radius: 50%;
}

/* Main Content Styles */
.main-content {
    -fx-background-color: #F5F5F5;
    -fx-spacing: 20;
}

/* Post Input Area Styles */
.post-input {
    -fx-background-color: #E3F2FD;
    -fx-padding: 10;
    -fx-background-radius: 5;
}

.post-textarea {
    -fx-pref-height: 100;
    -fx-background-color: #E3F2FD;
    -fx-background-radius: 8;
    -fx-padding: 10;
    -fx-font-size: 14px;
}

.post-button {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 5 10;
    -fx-background-radius: 5;
}

.category-dropdown {
    -fx-background-color: #F0F2F5;
    -fx-background-radius: 5;
}

.profile-circle {
    -fx-fill: #B0BEC5;
    -fx-stroke: #757575;
    -fx-stroke-width: 2;
}

/* Post Styles */
.post {
    -fx-background-color: #FFFFFF;
    -fx-padding: 10;
    -fx-background-radius: 5;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

.username {
    -fx-font-weight: bold;
}

.timestamp {
    -fx-text-fill: #757575;
}

.like-button {
    -fx-background-color: #1976D2;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 5 10;
    -fx-background-radius: 5;
}

.comment-button {
    -fx-background-color: #B0BEC5;
    -fx-text-fill: #000000;
    -fx-padding: 5 10;
    -fx-background-radius: 5;
}

/* Class Routine Styles */
.routine {
    -fx-background-color: #E0F7FA;
    -fx-padding: 10;
    -fx-background-radius: 5;
}

.section-header {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #1A237E;
}

.class-entry {
    -fx-padding: 5;
}

.subject {
    -fx-font-weight: bold;
    -fx-min-width: 100;
}

.time {
    -fx-background-color: #C8E6C9;
    -fx-padding: 5;
    -fx-background-radius: 3;
    -fx-min-width: 150;
}

.section {
    -fx-background-color: #FFCCBC;
    -fx-padding: 5;
    -fx-background-radius: 3;
    -fx-min-width: 80;
}

.room {
    -fx-background-color: #FFCCBC;
    -fx-padding: 5;
    -fx-background-radius: 3;
    -fx-min-width: 80;
}





/* Main content styles */
.main-content {
    background-color: #f0f2f5;
    -fx-background-color: #f0f2f5;
    padding: 20px;
    -fx-padding: 20;
    -fx-spacing: 20;
}

.content-header {
    -fx-spacing: 20;
    padding: 20px;
    -fx-padding: 20;
    background-color: white;
    -fx-background-color: white;
    border-radius: 10px;
    -fx-background-radius: 10;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
}

.welcome-section {
    -fx-spacing: 5;
}

.welcome-text {
    font-size: 16px;
    -fx-font-size: 16px;
    color: #666;
    -fx-text-fill: #666;
}

.user-name {
    font-size: 24px;
    -fx-font-size: 24px;
    font-weight: bold;
    -fx-font-weight: bold;
}

.stats-section {
    -fx-spacing: 30;
    display: flex;
    justify-content: flex-end;
    -fx-alignment: center-right;
}

.stat-card {
    padding: 15px;
    -fx-padding: 15;
    background-color: #e3f2fd;
    -fx-background-color: #e3f2fd;
    border-radius: 8px;
    -fx-background-radius: 8;
    text-align: center;
    -fx-alignment: center;
    min-width: 150px;
    -fx-min-width: 150;
}

.stat-label {
    font-size: 14px;
    -fx-font-size: 14px;
    color: #666;
    -fx-text-fill: #666;
}

.stat-value {
    font-size: 24px;
    -fx-font-size: 24px;
    font-weight: bold;
    -fx-font-weight: bold;
    color: #1a237e;
    -fx-text-fill: #1a237e;
}

.content-body {
    -fx-spacing: 20;
    display: flex;
    gap: 20px;
}

.feed-section {
    -fx-spacing: 15;
    background-color: white;
    -fx-background-color: white;
    border-radius: 10px;
    -fx-background-radius: 10;
    padding: 20px;
    -fx-padding: 20;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
}

.post-input {
    height: 100px;
    -fx-pref-height: 100;
    background-color: #f0f2f5;
    -fx-background-color: #f0f2f5;
    border-radius: 8px;
    -fx-background-radius: 8;
    padding: 10px;
    -fx-padding: 10;
    font-size: 14px;
    -fx-font-size: 14px;
}

.post-actions {
    -fx-spacing: 10;
    display: flex;
    gap: 10px;
}

.post-button {
    background-color: #1a237e;
    -fx-background-color: #1a237e;
    color: white;
    -fx-text-fill: white;
    padding: 8px 20px;
    -fx-padding: 8 20;
    border-radius: 5px;
    -fx-background-radius: 5;
}

.post-button:hover {
    background-color: #283593;
    -fx-background-color: #283593;
    cursor: pointer;
}

.category-dropdown {
    background-color: #f0f2f5;
    -fx-background-color: #f0f2f5;
    border-radius: 5px;
    -fx-background-radius: 5;
}

.feed-content {
    -fx-spacing: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.schedule-section {
    background-color: white;
    -fx-background-color: white;
    border-radius: 10px;
    -fx-background-radius: 10;
    padding: 20px;
    -fx-padding: 20;
    -fx-spacing: 15;
    min-width: 300px;
    -fx-min-width: 300;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
}

.section-header {
    font-size: 20px;
    -fx-font-size: 20px;
    font-weight: bold;
    -fx-font-weight: bold;
    color: #1a237e;
    -fx-text-fill: #1a237e;
}

.schedule-content {
    -fx-spacing: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Class routine styles - matching DBMS exactly */
.class-list {
    -fx-background-color: transparent;
    -fx-max-height: 400;
}

.class-item {
    -fx-background-color: white;
    -fx-border-width: 0 0 0 5;
    -fx-border-color: #000;
    -fx-background-radius: 5;
    -fx-padding: 15;
    -fx-spacing: 10;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
    -fx-margin: 0 0 15 0;
}

.class-item:hover {
    -fx-translate-y: -3;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.15), 12, 0, 0, 4);
}

.subject-math,
.subject-physics,
.subject-chemistry,
.subject-english {
    -fx-border-color: #000;
}

.class-details {
    -fx-spacing: 10;
}

.subject-label {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #000;
}

.class-time {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #000;
    -fx-spacing: 5;
    -fx-alignment: center-left;
}

.class-location {
    -fx-spacing: 8;
}

.section-tag {
    -fx-background-color: #e8f5e8;
    -fx-text-fill: #2e7d32;
    -fx-padding: 4 8;
    -fx-background-radius: 12;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.room-tag {
    -fx-background-color: #fff3e0;
    -fx-text-fill: #f57c00;
    -fx-padding: 4 8;
    -fx-background-radius: 12;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}
