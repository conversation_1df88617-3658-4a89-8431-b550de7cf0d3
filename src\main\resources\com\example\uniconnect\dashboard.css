/* Base styles - matching DBMS project exactly */
*, *::before, *::after {
    -fx-box-sizing: border-box;
}

* {
    -fx-margin: 0;
    -fx-padding: 0;
}

.root {
    -fx-font-size: 10px; /* 1rem = 10px */
    -fx-line-height: 1.5;
    -fx-font-smoothing-type: gray;
    -fx-font-family: 'Roboto';
}

.container {
    -fx-background-color: #e8f1f2;
    -fx-pref-height: 100vh;
    -fx-grid-lines-visible: false;
}

/* Header styles - matching DBMS */
.header {
    -fx-background-color: white;
    -fx-padding: 10 40;
    -fx-effect: dropshadow(three-pass-box, rgba(175,175,175,1), 3, 0, 0, 2);
    -fx-pref-height: 80;
}

.header-item1 {
    -fx-spacing: 20;
    -fx-alignment: center;
}

.search-bar {
    -fx-alignment: center-left;
}

.welcome-text {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.user-name {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.notification {
    -fx-spacing: 15;
    -fx-alignment: center-right;
}

.nav-icon {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 24px;
    -fx-cursor: hand;
    -fx-padding: 5;
}

.nav-icon:hover {
    -fx-background-color: rgba(0, 0, 0, 0.05);
    -fx-background-radius: 50%;
}

.profile-img {
    -fx-background-radius: 50%;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 4, 0, 0, 0);
}

/* Main content layout */
.main-content {
    -fx-background-color: #f5f5f5;
    -fx-padding: 20;
    -fx-spacing: 20;
}

/* Stats cards section */
.content-header {
    -fx-spacing: 20;
    -fx-padding: 0 0 20 0;
    -fx-alignment: CENTER_LEFT;
}

.stats-card {
    -fx-background-color: white;
    -fx-background-radius: 10;
    -fx-padding: 20;
    -fx-pref-width: 200;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 10, 0, 0, 2);
    -fx-alignment: CENTER;
}

.stat-label {
    -fx-font-size: 14;
    -fx-text-fill: #666;
}

.stat-value {
    -fx-font-size: 24;
    -fx-font-weight: bold;
    -fx-text-fill: #1a237e;
}

/* Feed section */
.content-body {
    -fx-spacing: 20;
    -fx-fill-width: true;
}

.feed-section {
    -fx-spacing: 20;
    -fx-background-color: transparent;
    -fx-pref-width: 700;
}

.create-post {
    -fx-background-color: white;
    -fx-background-radius: 10;
    -fx-padding: 20;
    -fx-spacing: 15;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

.post-input {
    -fx-background-color: #f0f2f5;
    -fx-background-radius: 8;
    -fx-padding: 10;
    -fx-pref-height: 100;
    -fx-font-size: 14;
}

.post-actions {
    -fx-spacing: 10;
    -fx-alignment: CENTER_LEFT;
}

.post-button {
    -fx-background-color: #1a237e;
    -fx-text-fill: white;
    -fx-padding: 8 20;
    -fx-background-radius: 5;
    -fx-font-size: 14;
}

.post-button:hover {
    -fx-background-color: #283593;
    -fx-cursor: hand;
}

.category-dropdown {
    -fx-background-color: #f0f2f5;
    -fx-background-radius: 5;
    -fx-padding: 5 10;
    -fx-pref-width: 150;
}

/* Upcoming section */
.upcoming-section {
    -fx-background-color: white;
    -fx-background-radius: 10;
    -fx-padding: 20;
    -fx-spacing: 15;
    -fx-pref-width: 300;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 10, 0, 0, 2);
    -fx-max-height: 400;
}

.section-header {
    -fx-font-size: 18;
    -fx-font-weight: bold;
    -fx-text-fill: #1a237e;
}

.upcoming-content {
    -fx-spacing: 10;
    -fx-fill-width: true;
}

/* Grid layout styles */
.container {
    -fx-background-color: #fafafa;
    -fx-grid-lines-visible: false;
}

/* Sidebar styles - matching DBMS */
.sidebar {
    -fx-background-color: #1a237e;
    -fx-padding: 20 10;
    -fx-spacing: 10;
    -fx-grid-row-span: 2;
}

.menu {
    -fx-spacing: 10;
    -fx-padding: 0 0 20 0;
}

.sidebar-logo {
    -fx-fit-width: 120;
    -fx-preserve-ratio: true;
}

.sidebar-button {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-size: 16;
    -fx-padding: 10 20;
    -fx-alignment: center-left;
    -fx-graphic-text-gap: 10;
    -fx-cursor: hand;
}

.sidebar-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-background-radius: 5;
}

.sidebar-button.active {
    -fx-background-color: rgba(255, 255, 255, 0.2);
    -fx-background-radius: 5;
}

.header-content {
    -fx-spacing: 20;
    display: flex;
    align-items: center;
    -fx-alignment: center-left;
}

.header-label {
    font-size: 24px;
    -fx-font-size: 24px;
    font-weight: 500;
    -fx-font-weight: 500;
}

.search-bar {
    background-color: rgb(226, 232, 240);
    -fx-background-color: rgb(226, 232, 240);
    border-radius: 10px;
    -fx-background-radius: 10;
    padding: 5px 15px;
    -fx-padding: 5 15;
    width: 400px;
    -fx-pref-width: 400;
}

/* Sidebar styles */
.sidebar {
    background-color: #1a237e;
    -fx-background-color: #1a237e;  /* Dark blue color */
    padding: 20px 10px;
    -fx-padding: 20 10;
    -fx-spacing: 10;
}

.sidebar-button {
    color: white;
    -fx-text-fill: white;
    font-size: 16px;
    -fx-font-size: 16px;
    padding: 10px 20px;
    -fx-padding: 10 20;
    background-color: transparent;
    -fx-background-color: transparent;
    border: none;
    -fx-border-color: transparent;
    text-align: left;
    -fx-alignment: center-left;
    min-width: 200px;
    -fx-min-width: 200;
}

.sidebar-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    -fx-background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    -fx-background-radius: 5;
    cursor: pointer;
}

.content {
    -fx-background-color: #e8f1f2;
    /* Removed background image for simplicity */
    -fx-padding: 10;
    -fx-hgap: 10; /* Equivalent to grid-gap for HBox */
}

.content-left {
    -fx-padding: 10;
    -fx-vgap: 10; /* Equivalent to grid-gap for VBox */
}

.content-right {
    -fx-padding: 10;
    -fx-vgap: 10;
}

.post-text-area {
    -fx-pref-height: 100;
    -fx-font-family: "Roboto";
    -fx-font-size: 14px;
}

.category-combo-box {
    -fx-pref-width: 150;
}

.post-button {
    -fx-background-color: #000;
    -fx-text-fill: #fff;
    -fx-padding: 5 15;
    -fx-font-size: 14px;
}

.post-button:hover {
    -fx-background-color: #333;
}

.date-label {
    -fx-font-size: 18px;
}

.class-routine {
    -fx-background-color: rgba(255, 255, 255, 0.7);
    -fx-padding: 10;
}

.routine-label {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
}

/* Main content styles */
.main-content {
    background-color: #f0f2f5;
    -fx-background-color: #f0f2f5;
    padding: 20px;
    -fx-padding: 20;
    -fx-spacing: 20;
}

.content-header {
    -fx-spacing: 20;
    padding: 20px;
    -fx-padding: 20;
    background-color: white;
    -fx-background-color: white;
    border-radius: 10px;
    -fx-background-radius: 10;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
}

.welcome-section {
    -fx-spacing: 5;
}

.welcome-text {
    font-size: 16px;
    -fx-font-size: 16px;
    color: #666;
    -fx-text-fill: #666;
}

.user-name {
    font-size: 24px;
    -fx-font-size: 24px;
    font-weight: bold;
    -fx-font-weight: bold;
}

.stats-section {
    -fx-spacing: 30;
    display: flex;
    justify-content: flex-end;
    -fx-alignment: center-right;
}

.stat-card {
    padding: 15px;
    -fx-padding: 15;
    background-color: #e3f2fd;
    -fx-background-color: #e3f2fd;
    border-radius: 8px;
    -fx-background-radius: 8;
    text-align: center;
    -fx-alignment: center;
    min-width: 150px;
    -fx-min-width: 150;
}

.stat-label {
    font-size: 14px;
    -fx-font-size: 14px;
    color: #666;
    -fx-text-fill: #666;
}

.stat-value {
    font-size: 24px;
    -fx-font-size: 24px;
    font-weight: bold;
    -fx-font-weight: bold;
    color: #1a237e;
    -fx-text-fill: #1a237e;
}

.content-body {
    -fx-spacing: 20;
    display: flex;
    gap: 20px;
}

.feed-section {
    -fx-spacing: 15;
    background-color: white;
    -fx-background-color: white;
    border-radius: 10px;
    -fx-background-radius: 10;
    padding: 20px;
    -fx-padding: 20;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
}

.post-input {
    height: 100px;
    -fx-pref-height: 100;
    background-color: #f0f2f5;
    -fx-background-color: #f0f2f5;
    border-radius: 8px;
    -fx-background-radius: 8;
    padding: 10px;
    -fx-padding: 10;
    font-size: 14px;
    -fx-font-size: 14px;
}

.post-actions {
    -fx-spacing: 10;
    display: flex;
    gap: 10px;
}

.post-button {
    background-color: #1a237e;
    -fx-background-color: #1a237e;
    color: white;
    -fx-text-fill: white;
    padding: 8px 20px;
    -fx-padding: 8 20;
    border-radius: 5px;
    -fx-background-radius: 5;
}

.post-button:hover {
    background-color: #283593;
    -fx-background-color: #283593;
    cursor: pointer;
}

.category-dropdown {
    background-color: #f0f2f5;
    -fx-background-color: #f0f2f5;
    border-radius: 5px;
    -fx-background-radius: 5;
}

.feed-content {
    -fx-spacing: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.schedule-section {
    background-color: white;
    -fx-background-color: white;
    border-radius: 10px;
    -fx-background-radius: 10;
    padding: 20px;
    -fx-padding: 20;
    -fx-spacing: 15;
    min-width: 300px;
    -fx-min-width: 300;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
}

.section-header {
    font-size: 20px;
    -fx-font-size: 20px;
    font-weight: bold;
    -fx-font-weight: bold;
    color: #1a237e;
    -fx-text-fill: #1a237e;
}

.schedule-content {
    -fx-spacing: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Class routine styles - matching DBMS exactly */
.class-list {
    -fx-background-color: transparent;
    -fx-max-height: 400;
}

.class-item {
    -fx-background-color: white;
    -fx-border-width: 0 0 0 5;
    -fx-border-color: #000;
    -fx-background-radius: 5;
    -fx-padding: 15;
    -fx-spacing: 10;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
    -fx-margin: 0 0 15 0;
}

.class-item:hover {
    -fx-translate-y: -3;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.15), 12, 0, 0, 4);
}

.subject-math,
.subject-physics,
.subject-chemistry,
.subject-english {
    -fx-border-color: #000;
}

.class-details {
    -fx-spacing: 10;
}

.subject-label {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #000;
}

.class-time {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #000;
    -fx-spacing: 5;
    -fx-alignment: center-left;
}

.class-location {
    -fx-spacing: 8;
}

.section-tag {
    -fx-background-color: #e8f5e8;
    -fx-text-fill: #2e7d32;
    -fx-padding: 4 8;
    -fx-background-radius: 12;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.room-tag {
    -fx-background-color: #fff3e0;
    -fx-text-fill: #f57c00;
    -fx-padding: 4 8;
    -fx-background-radius: 12;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}
