<?php
// Include authentication check
require_once 'check_auth.php';

// Get user information from session
$full_name = $_SESSION['full_name'] ?? 'User';
$student_id = $_SESSION['student_id'] ?? '';
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin-Dashboard</title>
    <link rel="stylesheet" href="style.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/@mdi/font@7.4.47/css/materialdesignicons.min.css"
      rel="stylesheet"
    />
  </head>

  <body>
    <div class="container">
      <!-- Header -->
      <div class="header">
        <div class="header-item1">
          <div class="search-bar">
            <h3>Good morning, <?php echo htmlspecialchars($full_name); ?></h3>
          </div>
          <div class="notification">
            <i class="mdi mdi-message-outline nav-icon"></i>
            <i class="mdi mdi-bell-ring-outline nav-icon"></i>
            <img
              src="images/tommy.jpg"
              alt="Profile"
              class="nav-icon profile-img"
              onerror="this.src='../images/uniconnect.png'; this.style.padding='2px';"
            />
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="sidebar">
        <nav>
          <div class="menu">
            <a href="#">
              <img
                src="../images/uniconnect.png"
                alt="Uniconnect Logo"
                style="width: 120px; height: auto; margin: 10px 0;"
              />
            </a>
          </div>

          <div class="menu-general">
            <a href="#" class="sidebar-item"
              ><i class="mdi mdi-card-account-details"></i>
              <span>Profile</span></a
            >
            <a href="#" class="sidebar-item active"
              ><i class="mdi mdi-home"></i> <span>Home</span></a
            >
            <a href="#" class="sidebar-item"
              ><i class="mdi mdi-account-group"></i> <span>Study Group</span></a
            >
            <a href="#" class="sidebar-item"
              ><i class="mdi mdi-note-text-outline"></i> <span>Notes</span></a
            >
            <a href="#" class="sidebar-item"
              ><i class="mdi mdi-tools"></i> <span>Academic Tools</span></a
            >
          </div>

          <div class="menu-settings">
            <a href="#" class="sidebar-item"
              ><i class="mdi mdi-cog"></i> <span>Settings</span></a
            >
            <a href="../logout.php" class="sidebar-item"
              ><i class="mdi mdi-logout"></i> <span>Logout</span></a
            >
          </div>
        </nav>
      </div>

      <!-- Main Content -->
      <div class="content">
        <div class="content-main">
          <!-- Left Side: Posts Feed -->
          <div class="content-left">
            <!-- Create Post -->
            <div class="create-post">
              <div class="post-input">
                <div class="create-post-avatar">
                  <img src="images/tommy.jpg" alt="Avatar" onerror="this.src='../images/uniconnect.png'; this.style.padding='2px';" />
                </div>
                <div class="post-input-content">
                  <textarea
                    placeholder="What's on your mind?"
                    aria-label="Create a post"
                  ></textarea>
                  <div class="post-options">
                    <select aria-label="Select post category">
                      <option value="" disabled selected>
                        Select Category
                      </option>
                      <option value="buy-sell">Buy & Sell</option>
                      <option value="academic">Academic</option>
                      <option value="lost-found">Lost & Found</option>
                    </select>
                    <label class="photo-upload">
                      <i class="mdi mdi-camera"></i> Photo
                      <input
                        type="file"
                        accept="image/*"
                        aria-label="Upload photo"
                      />
                    </label>
                    <button class="post-button">Post</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Posts Feed -->
            <div class="posts-feed">
              <h3>Recent Posts</h3>

              <!-- Post 1 -->
              <div class="post">
                <div class="post-header">
                  <div class="user-avatar">
                    <img src="images/grace.jpg" alt="User Avatar" onerror="this.src='../images/uniconnect.png'; this.style.padding='2px';" />
                  </div>
                  <div class="post-meta">
                    <div class="meta-top">
                      <h4 class="user-name">Grace</h4>
                      <span style="flex-grow: 1"></span>
                      <span class="category-tag">Academic</span>
                    </div>
                    <span class="timestamp">2 hours ago</span>
                    <div class="post-content">
                      <p>
                        Looking for CS101 notes for this trimester. Can anyone
                        share?
                      </p>
                    </div>
                    <div class="post-divider"></div>
                    <div class="post-actions">
                      <button class="action-btn">
                        <i class="mdi mdi-thumb-up-outline"></i> Like
                      </button>
                      <button class="action-btn">
                        <i class="mdi mdi-comment-outline"></i> Comment
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Post 2 -->
              <div class="post">
                <div class="post-header">
                  <div class="user-avatar">
                    <img src="images/arthur.jpg" alt="User Avatar" onerror="this.src='../images/uniconnect.png'; this.style.padding='2px';" />
                  </div>
                  <div class="post-meta">
                    <div class="meta-top">
                      <h4 class="user-name">Arthur</h4>
                      <span style="flex-grow: 1"></span>
                      <span class="category-tag">Buy & Sell</span>
                    </div>
                    <span class="timestamp">Yesterday</span>
                    <div class="post-content">
                      <p>
                        Selling my Statistics textbook. Barely used, perfect
                        condition. $45 OBO.
                      </p>
                      <img
                        src="images/statistics.jpg"
                        alt="Statistics Textbook"
                        class="post-image"
                        onerror="this.src='../images/uniconnect.png';"
                      />
                    </div>
                    <div class="post-divider"></div>
                    <div class="post-actions">
                      <button class="action-btn">
                        <i class="mdi mdi-thumb-up-outline"></i> Like
                      </button>
                      <button class="action-btn">
                        <i class="mdi mdi-comment-outline"></i> Comment
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Side: Friday and Routine -->
          <div class="right-side-container">
            <!-- Friday Grid -->
            <div class="friday-grid">
              <h3>Friday, 25 April, 2025</h3>
              <div class="calendar-grid">
                <h3>Calendar Status</h3>
                <p><span class="status-badge">In progress</span></p>
              </div>
            </div>

            <!-- Class Routine -->
            <div class="class-routine">
              <h3>Your Class Routine</h3>
              <div class="class-list">
                <div class="class-item subject-math">
                  <div class="class-details">
                    <h3>Mathematics</h3>
                    <p class="class-time">
                      <i class="mdi mdi-clock-outline"></i> 9:00 AM - 10:30 AM
                    </p>
                    <div class="class-location">
                      <span class="section-tag">Section A</span>
                      <span class="room-tag">Room 101</span>
                    </div>
                  </div>
                </div>

                <div class="class-item subject-physics">
                  <div class="class-details">
                    <h3>Physics</h3>
                    <p class="class-time">
                      <i class="mdi mdi-clock-outline"></i> 11:00 AM - 12:30 PM
                    </p>
                    <div class="class-location">
                      <span class="section-tag">Section B</span>
                      <span class="room-tag">Room 203</span>
                    </div>
                  </div>
                </div>

                <div class="class-item subject-chemistry">
                  <div class="class-details">
                    <h3>Chemistry</h3>
                    <p class="class-time">
                      <i class="mdi mdi-clock-outline"></i> 2:00 PM - 3:30 PM
                    </p>
                    <div class="class-location">
                      <span class="section-tag">Section A</span>
                      <span class="room-tag">Room 305</span>
                    </div>
                  </div>
                </div>

                <div class="class-item subject-english">
                  <div class="class-details">
                    <h3>English</h3>
                    <p class="class-time">
                      <i class="mdi mdi-clock-outline"></i> 4:00 PM - 5:30 PM
                    </p>
                    <div class="class-location">
                      <span class="section-tag">Section C</span>
                      <span class="room-tag">Room 112</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      // Handle like button interactions
      document.addEventListener("DOMContentLoaded", function () {
        const likeButtons = document.querySelectorAll(".action-btn");

        likeButtons.forEach((button) => {
          if (button.textContent.includes("Like")) {
            button.addEventListener("click", function () {
              if (this.classList.contains("liked")) {
                // Unlike
                this.classList.remove("liked");
                this.innerHTML =
                  '<i class="mdi mdi-thumb-up-outline"></i> Like';
              } else {
                // Like
                this.classList.add("liked");
                this.innerHTML = '<i class="mdi mdi-thumb-up"></i> Liked';
              }
            });
          }
        });
      });
    </script>
  </body>
</html>
