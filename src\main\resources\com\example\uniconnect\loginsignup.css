/* Root styling */
.root {
    -fx-background-color: #DFECF2; /* --white */
    -fx-font-family: 'Montserrat', sans-serif;
    -fx-font-size: 14px;
}

/* Container styling */
.stack-pane {
    -fx-background-color: #DFECF2;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 30, 0, 0, 0);
    -fx-background-radius: 20px;
    -fx-padding: 0;
}

/* Main container */
.main-container {
    -fx-background-color: white;
    -fx-background-radius: 20px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 30, 0, 0, 0);
    -fx-min-width: 900px;
    -fx-min-height: 550px;
    -fx-pref-width: 100%;
    -fx-pref-height: 100%;
}

/* Left panel */
.left-panel {
    -fx-background-color: #000000; /* --black */
    -fx-padding: 40px;
    -fx-alignment: center;
    -fx-spacing: 20px;
    -fx-background-radius: 20px 0 0 20px;
    -fx-max-width: 360px;
}

/* Welcome text */
.welcome-text {
    -fx-font-size: 24px;
    -fx-font-weight: normal;
    -fx-fill: white;
    -fx-font-family: 'Montserrat', sans-serif;
}

.highlight {
    -fx-fill: #1A73E8; /* --primary */
    -fx-font-weight: 800;
}

/* Logo animation */
.logo-image {
    -fx-effect: dropshadow(gaussian, rgba(255,255,255,0.3), 20, 0, 0, 0);
    -fx-opacity: 1;
    -fx-translate-y: 0;
    -fx-animation: fadeSlide 1s ease-out;
}

@keyframes fadeSlide {
    0% { -fx-opacity: 0; -fx-translate-y: 30px; }
    100% { -fx-opacity: 1; -fx-translate-y: 0; }
}

/* Form area */
.form-area {
    -fx-background-color: white;
    -fx-padding: 40px;
    -fx-alignment: center;
    -fx-background-radius: 0 20px 20px 0;
    -fx-min-width: 540px;
}

/* Form section */
.form-section {
    -fx-padding: 20px;
    -fx-alignment: center;
    -fx-spacing: 20px;
    -fx-min-width: 300px;
    -fx-max-width: 300px;
    -fx-min-height: 400px;
}

/* Form title */
.form-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #000000;
    -fx-font-family: 'Montserrat', sans-serif;
    -fx-padding: 0 0 20px 0;
    -fx-text-alignment: center;
}

/* Input fields */
.text-field, .password-field, .combo-box {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #ccc;
    -fx-border-radius: 8px;
    -fx-padding: 12px 20px;
    -fx-font-size: 14px;
    -fx-font-family: 'Montserrat', sans-serif;
    -fx-pref-height: 40px;
    -fx-pref-width: 300px;
    -fx-max-width: 300px;
}

.text-field:focused, .password-field:focused, .combo-box:focused {
    -fx-border-color: #1A73E8;
    -fx-background-color: #F8FBFF;
}

/* ComboBox styling */
.combo-box {
    -fx-background-color: white;
    -fx-text-fill: #333;
}

.combo-box .list-cell {
    -fx-text-fill: #333;
    -fx-padding: 8px;
    -fx-font-family: 'Montserrat', sans-serif;
}

.combo-box .list-view {
    -fx-background-color: white;
    -fx-border-color: #ccc;
}

/* Buttons */
.button {
    -fx-background-color: #000000;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 12px;
    -fx-font-size: 16px;
    -fx-cursor: hand;
    -fx-pref-height: 45px;
    -fx-pref-width: 300px;
    -fx-max-width: 300px;
    -fx-font-family: 'Montserrat', sans-serif;
}

.button:hover {
    -fx-background-color: #1A73E8;
}

/* Loading effect for buttons */
.button.loading {
    -fx-background-color: #1A73E8;
    -fx-cursor: wait;
    -fx-opacity: 0.9;
    -fx-animation: spin 1s infinite linear;
}

@keyframes spin {
    0% { -fx-rotate: 0deg; }
    100% { -fx-rotate: 360deg; }
}

/* Toggle link */
.toggle-link {
    -fx-font-size: 14px;
    -fx-text-fill: #000000;
    -fx-font-family: 'Montserrat', sans-serif;
    -fx-padding: 10px 0 0 0;
    -fx-alignment: center;
    -fx-min-height: 40px;
}

.toggle-link .hyperlink {
    -fx-text-fill: #1A73E8;
    -fx-border-color: transparent;
    -fx-padding: 0;
    -fx-underline: false;
    -fx-cursor: hand;
}

.toggle-link .hyperlink:hover {
    -fx-underline: true;
    -fx-text-fill: #0D47A1;
}

/* Form transitions */
#loginForm, #signupForm {
    -fx-opacity: 1;
    -fx-translate-x: 0;
    -fx-animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    0% { -fx-opacity: 0; -fx-translate-x: 20px; }
    100% { -fx-opacity: 1; -fx-translate-x: 0; }
}