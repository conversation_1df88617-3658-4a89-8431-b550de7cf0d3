<?xml version="1.0" encoding="UTF-8"?>

<?import java.net.URL?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Text?>
<?import javafx.scene.text.TextFlow?>

<StackPane minHeight="550.0" minWidth="900.0" prefHeight="550.0" prefWidth="900.0" styleClass="stack-pane" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.uniconnect.LoginSignupController">
    <stylesheets>
        <URL value="@loginsignup.css" />
    </stylesheets>

    <!-- Main Container -->
    <HBox styleClass="main-container" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS" fillHeight="true">
        <!-- Left Panel (Black Side) -->
        <VBox styleClass="left-panel" HBox.hgrow="NEVER" minWidth="360.0" prefWidth="360.0" alignment="CENTER">
            <ImageView fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true" styleClass="logo-image">
                <image>
                    <Image url="@images/uniconnect.png" />
                </image>
            </ImageView>

            <TextFlow prefWidth="280.0" textAlignment="CENTER">
                <Text styleClass="welcome-text" text="Welcome to " />
                <Text styleClass="welcome-text, highlight" text="Uniconnect" />
            </TextFlow>
        </VBox>

        <!-- Form Area (Right Side) -->
        <VBox styleClass="form-area" HBox.hgrow="ALWAYS" minWidth="540.0" alignment="CENTER">
            <StackPane alignment="CENTER">
                <!-- Login Form -->
                <VBox fx:id="loginForm" styleClass="form-section" visible="true" spacing="20" minHeight="400.0">
                    <Label styleClass="form-title" text="Login" />

                    <TextField fx:id="studentIdField" promptText="Student ID" styleClass="text-field" />
                    <PasswordField fx:id="passwordField" promptText="Password" styleClass="password-field" />

                    <Button fx:id="loginButton" onAction="#loginBtn" styleClass="button" text="Login" />

                    <HBox styleClass="toggle-link" alignment="CENTER" spacing="5">
                        <Label text="Don't have an account? " />
                        <Hyperlink onAction="#showSignupForm" text="Sign Up" />
                    </HBox>
                </VBox>

                <!-- Signup Form -->
                <VBox fx:id="signupForm" styleClass="form-section" visible="false" spacing="20" minHeight="500.0">
                    <Label styleClass="form-title" text="Sign Up" />

                    <TextField fx:id="suFullName" promptText="Full Name" styleClass="text-field" />
                    <TextField fx:id="suStudentId" promptText="Student ID" styleClass="text-field" />
                    <TextField fx:id="suEmail" promptText="University Email" styleClass="text-field" />
                    <PasswordField fx:id="suPassword" promptText="Password" styleClass="password-field" />
                    <PasswordField fx:id="suConfirmPassword" promptText="Confirm Password" styleClass="password-field" />

                    <ComboBox fx:id="suDepartment" promptText="Select Department" styleClass="combo-box" prefWidth="300.0" />

                    <Button fx:id="signupButton" onAction="#regBtn" styleClass="button" text="Sign Up" />

                    <HBox styleClass="toggle-link" alignment="CENTER" spacing="5">
                        <Label text="Already have an account? " />
                        <Hyperlink onAction="#showLoginForm" text="Login" />
                    </HBox>
                </VBox>
            </StackPane>
        </VBox>
    </HBox>
</StackPane>