package com.example.uniconnect;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.animation.FadeTransition;
import javafx.animation.TranslateTransition;
import javafx.animation.ParallelTransition;
import javafx.stage.Stage;
import javafx.util.Duration;
import java.sql.*;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;
import java.io.IOException;

public class LoginSignupController {
    // Login form elements
    @FXML private VBox loginForm;
    @FXML private TextField studentIdField;
    @FXML private PasswordField passwordField;
    @FXML private Button loginButton;

    // Signup form elements
    @FXML private VBox signupForm;
    @FXML private TextField suFullName;
    @FXML private TextField suStudentId;
    @FXML private TextField suEmail;
    @FXML private PasswordField suPassword;
    @FXML private PasswordField suConfirmPassword;
    @FXML private ComboBox<String> suDepartment;
    @FXML private Button signupButton;

    private static final Logger LOGGER = Logger.getLogger(LoginSignupController.class.getName());

    @FXML
    public void initialize() {
        // Initialize department options
        suDepartment.getItems().addAll(
            "CSE", "BBA", "EEE", "BSDS", "EDS", "MSJB", "PHARM", "BGE"
        );

        // Show login form by default
        loginForm.setVisible(true);
        signupForm.setVisible(false);

        // Add initial styling for animations
        loginForm.setOpacity(1.0);
        signupForm.setOpacity(0.0);
    }

    @FXML
    public void showLoginForm() {
        // Create fade out animation for signup form
        FadeTransition fadeOut = new FadeTransition(Duration.millis(300), signupForm);
        fadeOut.setFromValue(1.0);
        fadeOut.setToValue(0.0);

        // Create translate animation for signup form
        TranslateTransition translateOut = new TranslateTransition(Duration.millis(300), signupForm);
        translateOut.setByX(20);

        // Create fade in animation for login form
        FadeTransition fadeIn = new FadeTransition(Duration.millis(300), loginForm);
        fadeIn.setFromValue(0.0);
        fadeIn.setToValue(1.0);

        // Create translate animation for login form
        TranslateTransition translateIn = new TranslateTransition(Duration.millis(300), loginForm);
        translateIn.setFromX(-20);
        translateIn.setToX(0);

        // Run animations in sequence
        fadeOut.setOnFinished(e -> {
            signupForm.setVisible(false);
            loginForm.setVisible(true);

            // Run fade in and translate animations in parallel
            ParallelTransition parallelIn = new ParallelTransition(fadeIn, translateIn);
            parallelIn.play();
        });

        // Run fade out and translate animations in parallel
        ParallelTransition parallelOut = new ParallelTransition(fadeOut, translateOut);
        parallelOut.play();
    }

    @FXML
    public void showSignupForm() {
        // Create fade out animation for login form
        FadeTransition fadeOut = new FadeTransition(Duration.millis(300), loginForm);
        fadeOut.setFromValue(1.0);
        fadeOut.setToValue(0.0);

        // Create translate animation for login form
        TranslateTransition translateOut = new TranslateTransition(Duration.millis(300), loginForm);
        translateOut.setByX(-20);

        // Create fade in animation for signup form
        FadeTransition fadeIn = new FadeTransition(Duration.millis(300), signupForm);
        fadeIn.setFromValue(0.0);
        fadeIn.setToValue(1.0);

        // Create translate animation for signup form
        TranslateTransition translateIn = new TranslateTransition(Duration.millis(300), signupForm);
        translateIn.setFromX(20);
        translateIn.setToX(0);

        // Run animations in sequence
        fadeOut.setOnFinished(e -> {
            loginForm.setVisible(false);
            signupForm.setVisible(true);

            // Run fade in and translate animations in parallel
            ParallelTransition parallelIn = new ParallelTransition(fadeIn, translateIn);
            parallelIn.play();
        });

        // Run fade out and translate animations in parallel
        ParallelTransition parallelOut = new ParallelTransition(fadeOut, translateOut);
        parallelOut.play();
    }

    @FXML
    public void loginBtn() {
        // Add loading effect to button
        loginButton.getStyleClass().add("loading");

        if (studentIdField.getText().isEmpty() || passwordField.getText().isEmpty()) {
            showAlert(Alert.AlertType.ERROR, "Error", "Please fill in all fields.");
            loginButton.getStyleClass().remove("loading");
            return;
        }

        try (Connection conn = Database.connectDB()) {
            String sql = "SELECT * FROM users WHERE student_id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, studentIdField.getText().trim());
                ResultSet rs = stmt.executeQuery();

                if (rs.next() && verifyPassword(passwordField.getText(), rs.getString("password"))) {
                    // Store user data in Data class
                    Data.userId = rs.getInt("id");
                    Data.studentId = rs.getString("student_id");
                    Data.fullName = rs.getString("full_name");

                    // Remove loading effect
                    loginButton.getStyleClass().remove("loading");

                    // Navigate to dashboard
                    try {
                        // Show success message first
                        showAlert(Alert.AlertType.INFORMATION, "Success", "Login successful!");

                        // Then load dashboard
                        LOGGER.log(Level.INFO, "Attempting to load Dashboard.fxml");
                        FXMLLoader loader = new FXMLLoader();
                        loader.setLocation(getClass().getResource("Dashboard.fxml"));

                        try {
                            Scene scene = new Scene(loader.load(), 1000, 700);

                            // Get current stage
                            Stage stage = (Stage) loginButton.getScene().getWindow();
                            stage.setScene(scene);
                            stage.setTitle("UniConnect Dashboard");
                            stage.setMinWidth(1000);
                            stage.setMinHeight(700);
                            stage.show();
                        } catch (IOException e) {
                            LOGGER.log(Level.SEVERE, "Error loading dashboard content", e);

                            // Print detailed information about the resource
                            String resourcePath = "Dashboard.fxml";
                            LOGGER.log(Level.INFO, "Resource path: " + resourcePath);
                            LOGGER.log(Level.INFO, "Resource URL: " + getClass().getResource(resourcePath));

                            // Check if the file exists
                            if (getClass().getResource(resourcePath) == null) {
                                LOGGER.log(Level.SEVERE, "Dashboard.fxml file not found in resources");
                                showAlert(Alert.AlertType.ERROR, "Error", "Dashboard.fxml file not found in resources");
                            } else {
                                showAlert(Alert.AlertType.ERROR, "Error", "Could not load dashboard: " + e.getMessage());
                            }
                            e.printStackTrace();
                        }
                    } catch (Exception e) {
                        LOGGER.log(Level.SEVERE, "Unexpected error loading dashboard", e);
                        showAlert(Alert.AlertType.ERROR, "Error", "Unexpected error loading dashboard: " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    // Remove loading effect
                    loginButton.getStyleClass().remove("loading");

                    showAlert(Alert.AlertType.ERROR, "Error", "Invalid student ID or password.");
                }
            }
        } catch (SQLException e) {
            // Remove loading effect
            loginButton.getStyleClass().remove("loading");

            LOGGER.log(Level.SEVERE, "Database error during login", e);
            showAlert(Alert.AlertType.ERROR, "Error", "Database error occurred.");
        }
    }

    @FXML
    public void regBtn() {
        // Add loading effect to button
        signupButton.getStyleClass().add("loading");

        // Validate all fields
        if (suFullName.getText().isEmpty() || suStudentId.getText().isEmpty() ||
            suEmail.getText().isEmpty() || suPassword.getText().isEmpty() ||
            suConfirmPassword.getText().isEmpty() || suDepartment.getValue() == null) {
            showAlert(Alert.AlertType.ERROR, "Error", "Please fill in all fields.");
            signupButton.getStyleClass().remove("loading");
            return;
        }

        // Check if passwords match
        if (!suPassword.getText().equals(suConfirmPassword.getText())) {
            showAlert(Alert.AlertType.ERROR, "Error", "Passwords do not match.");
            signupButton.getStyleClass().remove("loading");
            return;
        }

        try (Connection conn = Database.connectDB()) {
            String sql = "INSERT INTO users (full_name, student_id, email, password, department) VALUES (?, ?, ?, ?, ?)";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, suFullName.getText().trim());
                stmt.setString(2, suStudentId.getText().trim());
                stmt.setString(3, suEmail.getText().trim());
                stmt.setString(4, hashPassword(suPassword.getText()));
                stmt.setString(5, suDepartment.getValue());

                stmt.executeUpdate();

                // Remove loading effect
                signupButton.getStyleClass().remove("loading");

                showAlert(Alert.AlertType.INFORMATION, "Success", "Registration successful!");
                clearRegistrationFields();
                // Switch to login form after successful registration
                showLoginForm();
            }
        } catch (SQLException e) {
            // Remove loading effect
            signupButton.getStyleClass().remove("loading");

            if (e.getSQLState().startsWith("23")) {
                showAlert(Alert.AlertType.ERROR, "Error", "Student ID or email already exists.");
            } else {
                LOGGER.log(Level.SEVERE, "Database error during registration", e);
                showAlert(Alert.AlertType.ERROR, "Error", "Database error occurred.");
            }
        }
    }

    private void showAlert(Alert.AlertType type, String title, String content) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }

    private void clearRegistrationFields() {
        suFullName.clear();
        suStudentId.clear();
        suEmail.clear();
        suPassword.clear();
        suConfirmPassword.clear();
        suDepartment.setValue(null);
    }



    /**
     * Hash a password using SHA-256
     * @param password The password to hash
     * @return The hashed password
     */
    private String hashPassword(String password) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(password.getBytes(StandardCharsets.UTF_8));

            // Convert to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            LOGGER.log(Level.SEVERE, "Error hashing password", e);
            return null;
        }
    }

    /**
     * Verify a password against a stored hash
     * @param password The password to verify
     * @param storedHash The stored hash
     * @return True if the password matches the hash
     */
    private boolean verifyPassword(String password, String storedHash) {
        String hashedPassword = hashPassword(password);
        return hashedPassword != null && hashedPassword.equals(storedHash);
    }
}
