package com.example.uniconnect;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.stage.Stage;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UniConnectApplication extends Application {
    private static final Logger LOGGER = Logger.getLogger(UniConnectApplication.class.getName());

    @Override
    public void start(Stage stage) {
        try {
            FXMLLoader fxmlLoader = new FXMLLoader(UniConnectApplication.class.getResource("LoginSignup.fxml"));
            Scene scene = new Scene(fxmlLoader.load(), 900, 550);
            stage.setTitle("UniConnect");
            stage.setScene(scene);
            stage.setMinWidth(900);
            stage.setMinHeight(550);
            stage.show();
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "Failed to load FXML file", e);
            showErrorAndExit("Application Error", "Failed to load application: " + e.getMessage());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Unexpected error", e);
            showErrorAndExit("Application Error", "An unexpected error occurred: " + e.getMessage());
        }
    }

    private void showErrorAndExit(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR, message, ButtonType.OK);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.showAndWait().ifPresent(response -> Platform.exit());
    }

    public static void main(String[] args) {
        launch();
    }
}
