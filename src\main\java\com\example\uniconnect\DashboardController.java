package com.example.uniconnect;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.image.ImageView;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class DashboardController {
    @FXML
    private GridPane dashboardContainer;

    @FXML
    private HBox header;

    @FXML
    private VBox sidebar;

    @FXML
    private VBox contentLeft;

    @FXML
    private VBox contentRight;

    @FXML
    private Label userNameLabel;

    @FXML
    private Label userFullNameLabel;

    @FXML
    private Label dateLabel;

    @FXML
    private ImageView profileImageView;

    @FXML
    private TextArea postTextArea;

    @FXML
    private ComboBox<String> categoryComboBox;

    @FXML
    private Button postButton;

    @FXML
    private Button logoutButton;

    @FXML
    private Button dashboardButton;

    @FXML
    public void initialize() {
        // Set user name from Data class
        if (Data.fullName != null && !Data.fullName.isEmpty()) {
            userNameLabel.setText(Data.fullName);
        } else {
            userNameLabel.setText("User");
        }

        // Set current date
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd MMMM, yyyy");
        dateLabel.setText(now.format(formatter));

        // Initialize category ComboBox
        categoryComboBox.getItems().addAll(
            "Academic", "Buy & Sell", "Lost & Found", "Events", "General"
        );
    }

    @FXML
    public void handlePost() {
        // This method would handle posting new content
        // For now, just clear the text area and reset the category
        if (postTextArea.getText().isEmpty()) {
            return;
        }

        // Here you would typically save the post to a database
        // For now, just clear the input fields
        postTextArea.clear();
        categoryComboBox.setValue(null);
    }

    @FXML
    public void handleLogout() {
        try {
            // Clear user data
            Data.userId = 0;
            Data.studentId = null;
            Data.fullName = null;

            // Load login screen
            FXMLLoader loader = new FXMLLoader(getClass().getResource("LoginSignup.fxml"));
            Scene scene = new Scene(loader.load(), 900, 550);

            // Get current stage
            Stage stage = (Stage) userNameLabel.getScene().getWindow();
            stage.setScene(scene);
            stage.setTitle("UniConnect");
            stage.setMinWidth(900);
            stage.setMinHeight(550);
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}