

*, *::before, *::after {
  box-sizing: border-box;
}

/* 2. Remove default margin */
* {
  margin: 0;
  padding: 0;
}

:root {
  font-size: 62.5%; /* 1rem = 10px */
}

body {
  /* 3. Add accessible line-height */
  line-height: 1.5;
  /* 4. Improve text rendering */
  -webkit-font-smoothing: antialiased;
  font-family: 'roboto';

}

/* 5. Improve media defaults */
img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
}

/* 6. Inherit fonts for form controls */
input, button, textarea, select {
  font: inherit;
}

/* 7. Avoid text overflows */
p, h1, h2, h3, h4, h5, h6 {
  overflow-wrap: break-word;
}

/* 8. Improve line wrapping */
p {
  text-wrap: pretty;
}
h1, h2, h3, h4, h5, h6 {
  text-wrap: balance;
}

/*
  9. Create a root stacking context
*/
#root, #__next {
  isolation: isolate;
}


.container{
  display: grid;
  grid-template-columns: 1fr 4fr;
  grid-template-rows: 1fr 4fr;
  height: 100vh;

}

.header{
  background-color: #fff;
  padding: 1rem 4rem;
  display: grid;
  gap: 1rem;
  position:relative;
  box-shadow: 0px 2px 3px rgb(175, 175, 175);
}

.header-item1 {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
  align-items: center;
}

.search-bar {
  display: flex;
  align-items: center;
}

.search-bar h3{
  font-size: 24px;
  font-weight: 500;
}

.search-bar i {
font-size: 2rem;
}

.search-bar input{
padding: 0.5rem 15em;
border-radius: 10px;
border: none;
background-color:rgba(226, 232, 240, 255);
}

.notification{
  display: flex;
  gap: 1.5rem;
  align-items: center;
  justify-self: end;
  margin-right: 1rem;
}

.notification i {
  font-size: 2.4rem;
  color: #000;
}

.notification img{
  height: 4.5rem;
  width: 4.5rem;
  border-radius: 50%;
  border: 2px solid #f0f0f0;
}

/* Navigation icon hover effects */
.nav-icon {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.5rem;
  border-radius: 50%;
}

.nav-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.header-item2 {
display: grid;
grid-template-columns: 3fr 2fr;
gap: 2rem;

}

.profile{
display: grid;
grid-template-columns: auto 1fr;
align-items: center;
gap: 2rem;
}
.profile img{
width: 8rem;
height: 8rem;
border-radius: 50%;
}
.profile-text p{
font-size: 1.5rem;
font-weight: bold;
}
.profile h3 {
font-size: 2rem;
}

.post{
display: grid;
grid-template-columns: auto auto 1fr;
gap: 2em;
align-items: center;
justify-self: end;
}

.post button {
padding: .5rem 4rem;
font-size: 1.5rem;
border: none;
cursor: pointer;
background-color:  #1992D4;
color: #fff;
border-radius: 1.5rem;
}


.sidebar{
  /* background-color: #1992D4; */
  background-color: #000;
  grid-row: 1 / 3;
  padding: 1rem;

}

nav{
  display: grid;
  grid-template-rows: auto auto 1fr;
  gap: 3rem;

}

.menu a {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
}

.menu a i{
  font-size: 4rem;
  color: #fff;

}

.menu a span {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 600;
}

.menu-general, .menu-settings {
  display: grid;

}
.menu-general a, .menu-settings a {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  gap: 2rem;
  text-decoration: none;
  margin-left: 1rem;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.menu-general a i, .menu-settings a i {
  font-size: 2rem;
  color: #fff;
}

.menu-general a span, .menu-settings a span{
  font-size: 1.5rem;
  color: #fff;
  font-weight: 500;
}

/* Active sidebar item */
.sidebar-item.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 4px solid #3f88c5;
  padding-left: 8px;
}

/* Hover effect for sidebar items */
.sidebar-item:not(.active):hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateX(3px);
}

.content {
  background-color: #e8f1f2;
  background-image: linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px),
                    linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  padding: 2rem 1.5rem;
  display: grid;
  grid-template-columns: 1fr; /* Single column layout */
  gap: 2rem;
}

.content-main {
  display: grid;
  grid-template-columns: 1fr 350px; /* Left content takes available space, right side fixed width */
  gap: 2rem;
  height: calc(100vh - 120px); /* Adjust based on header height */
}

.content-left > h3, .content-right > div > h3{
  font-size: 2rem;
}

/* Right side container for Friday grid and class routine */
.right-side-container {
  display: grid;
  grid-template-rows: auto 1fr; /* Auto height for Friday grid, remaining for class routine */
  gap: 2rem;
  justify-items: end; /* Align children to the right */
  height: 100%; /* Match the height of content-left */
  overflow-y: auto; /* Allow scrolling if needed */
}

.cards{
display: grid;
grid-template-columns: 1fr 1fr;
grid-template-rows: 1fr 1fr 1fr;
gap: 1.5rem;

}

.card-item{
background-color: #fff;
border-left: 5px solid #1992d4a8;
border-radius: 5px;
padding: 1.5rem 1rem ;
display: grid;
grid-template-rows: auto auto 1fr;
gap: 2rem;

}

.title{
font-size: 1.8rem;
}
.quote{
font-size: 1.6rem;
}

.share-icon{
  font-size: 2.5rem;
  display: grid;
  grid-template-columns: auto auto auto;
  gap: 2rem;
  justify-self: end;
  align-self: end;
  cursor: pointer;

}

.content-right {
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 2rem;
}

.card-small{
  background-color: #fff;
  padding: 2rem 1.5rem;
  border-radius: 5px;
}

.announce {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  height: 200px; /* Increased height */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.announce .card-small {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.announce h3 {
  font-size: 2.4rem; /* Increased font size */
  color: #333;
  text-align: center;
}

/* Friday Grid Styling */
.friday-grid {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 180px; /* Increased height */
  width: 80%; /* Decreased width */
  margin: 0 0 0 auto; /* Push to the right side */
}

.friday-grid h3 {
  font-size: 2.4rem;
  color: #333;
  text-align: center;
  margin-bottom: 1rem;
}

.maintenance h3, .community h3,.privacy h3 {
  font-size: 1.4rem;
}
.maintenance p, .community p, .privacy p  {
  font-size: 1rem;
  text-align: justify;
}

.maintenance, .community{
  padding-bottom: 1rem;
  border-bottom: 1px solid #d9d0d0;
}

.community, .privacy {
  margin-top: 1rem;
}

/* Calendar grid styling */
.calendar-grid {
  margin-top: 1.5rem;
  display: grid;
  width: 100%;
  text-align: center;
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 6px;
}

.calendar-grid h3 {
  font-size: 1.8rem;
  margin-bottom: 0.8rem;
  color: #444;
}

.calendar-grid p {
  font-size: 1.6rem;
  color: #666;
  font-weight: 400;
  text-align: center;
}

/* Status badge styling */
.status-badge {
  background-color: #e0e0e0;
  color: #333;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 1.4rem;
  font-weight: 500;
  display: inline-block;
  border: 1px solid #d0d0d0;
  transition: all 0.2s ease;
}

.status-badge:hover {
  background-color: #d0d0d0;
  color: #000;
}

.right-section {
  width: 100%; /* Changed from fixed 350px to 100% to be responsive */
  display: grid;
  grid-template-rows: 150px 1fr; /* Fixed height for date, remaining for routine */
  gap: 2rem;
  min-height: calc(100vh - 120px); /* Adjust based on your header height */
}

/* Style for the class routine section */
.class-routine {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  height: 340px; /* Adjusted height since images were removed */
  display: flex;
  flex-direction: column;
  margin-top: 1rem; /* Add some spacing from the Friday grid */
  width: 80%; /* Match width of Friday grid */
}

.class-routine h3 {
  padding: 2rem;
  margin: 0;
  font-size: 2rem;
  color: #000;
  font-weight: 600;
  border-bottom: 1px solid #000;
}

.class-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scrollbar-width: thin;
  scrollbar-color: #000 #e0e0e0;
  background-color: transparent;
}

.class-list::-webkit-scrollbar {
  width: 6px;
}

.class-list::-webkit-scrollbar-track {
  background: #e0e0e0;
  border-radius: 3px;
}

.class-list::-webkit-scrollbar-thumb {
  background: #000;
  border-radius: 3px;
}

.class-item {
  padding: 1.8rem 1.5rem;
  display: grid;
  grid-template-columns: 1fr;
  align-items: center;
  border-radius: 8px;
  margin-bottom: 1.2rem;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
  cursor: pointer;
  border-left: 5px solid #000;
}

.class-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* All subjects use black border */
.subject-math,
.subject-physics,
.subject-chemistry,
.subject-english {
  border-left-color: #000;
}

.class-details h3 {
  font-size: 1.8rem;
  color: #000;
  margin-bottom: 1rem;
  font-weight: 600;
  padding: 0;
  border: none;
}

.class-details .class-time {
  font-size: 1.6rem;
  color: #000;
  margin-bottom: 1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.class-details .class-time i {
  margin-right: 0.5rem;
  color: #000;
  font-size: 1.6rem;
}

.class-location {
  display: flex;
  gap: 0.8rem;
  margin-top: 0.8rem;
}

.section-tag, .room-tag {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 1.2rem;
  font-weight: 500;
  display: inline-block;
  border: 1px solid #000;
}

.section-tag {
  background-color: #f0f0f0;
  color: #000;
}

.room-tag {
  background-color: #f0f0f0;
  color: #000;
}

/* Styles for the main content area */
.content-left {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
  overflow-y: auto;
}

/* Create Post Section */
.create-post {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  padding: 1.5rem;
  border-left: 5px solid #000;
}

.post-input {
  display: flex;
  gap: 1rem;
}

.create-post-avatar {
  flex-shrink: 0;
}

.create-post-avatar img {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e0e0e0;
}

.post-input-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.post-input textarea {
  width: 100%;
  height: 80px;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: inherit;
  font-size: 1.4rem;
  resize: none;
}

.post-options {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.post-options select {
  padding: 0.8rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1.3rem;
  background-color: #fff;
  color: #333;
  transition: all 0.2s ease;
  cursor: pointer;
}

.post-options select:hover {
  border-color: #999;
  color: #000;
}

.post-options select:focus {
  border-color: #000;
  outline: none;
}

.photo-upload {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background-color: #f0f0f0;
  border: 1px solid #000;
  border-radius: 4px;
  font-size: 1.3rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
}

.photo-upload:hover {
  background-color: #e0e0e0;
  color: #000;
}

.photo-upload input {
  display: none;
}

.post-button {
  padding: 0.8rem 2rem;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1.3rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: auto;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.post-button:hover {
  background-color: #333;
}

.post-button:active {
  background-color: #000;
  transform: translateY(1px);
  box-shadow: none;
}

/* Posts Feed Section */
.posts-feed {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  /* Removed overflow-y: auto to prevent scrolling */
}

.posts-feed h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #000;
  margin-bottom: 0.5rem;
}

.post {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e0e0e0;
}

.post-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.2rem;
}

.user-avatar {
  margin-right: 1rem;
}

.user-avatar img {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e0e0e0;
}

.post-meta {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.meta-top {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 0.3rem;
  position: relative; /* For absolute positioning of category tag */
}

.post-meta h4 {
  font-size: 1.6rem;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.category-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.3rem 0.8rem;
  background-color: #000;
  color: #fff;
  border-radius: 4px;
  font-size: 1.1rem;
  font-weight: 500;
  position: absolute;
  right: 0;
  top: 0;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.category-tag:hover {
  background-color: #333;
}

.timestamp {
  font-size: 1.2rem;
  color: #606770;
  display: block;
  margin-bottom: 0.8rem;
}

.post-content {
  margin-top: 0.8rem;
  margin-bottom: 1.2rem;
  width: 100%;
}

.post-content p {
  font-size: 1.5rem;
  line-height: 1.5;
  color: #1c1e21;
  margin: 0;
}

.post-image {
  width: 100%;
  border-radius: 8px;
  margin-top: 1.2rem;
  border: 1px solid #e0e0e0;
}

/* Post engagement section */
.post-engagement {
  display: flex;
  flex-direction: column;
}

.post-divider {
  height: 1px;
  background-color: #e4e6eb;
  margin: 1rem 0 0.5rem 0;
  width: 100%;
}

.post-actions {
  display: flex;
  gap: 1rem;
  padding: 0.5rem 0;
  width: 100%;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: none;
  border: 1px solid transparent;
  font-size: 1.4rem;
  color: #333; /* Dark gray color for text */
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-weight: 500;
  flex: 1;
}

.action-btn:hover {
  background-color: #e9e9e9; /* Light gray background on hover */
  color: #000; /* Black on hover */
  border-color: #d0d0d0;
}

.action-btn:active {
  background-color: #e0e0e0;
}

/* Add a class for the liked state */
.action-btn.liked {
  color: #000; /* Black for liked state */
  font-weight: 600;
}

.action-btn.liked i {
  color: #000; /* Black for liked state icon */
}

.action-btn i {
  font-size: 1.6rem;
  color: #000; /* Black color for icons */
}

/* Override any Material Design Icons default colors */
.mdi-thumb-up-outline,
.mdi-thumb-up,
.mdi-comment-outline,
.mdi-comment {
  color: #000 !important; /* Force black color */
}


