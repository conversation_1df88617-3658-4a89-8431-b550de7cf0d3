package com.example.uniconnect;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;
import java.util.logging.Level;

public class Database {
    private static final String URL = "*****************************************************************************************************";
    private static final String USER = "root";
    private static final String PASSWORD = "shanto7890";
    private static final Logger LOGGER = Logger.getLogger(Database.class.getName());

    public static Connection connectDB() {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
            LOGGER.log(Level.INFO, "Successfully connected to the database.");
            createTablesIfNotExist(conn);
            return conn;
        } catch (ClassNotFoundException e) {
            LOGGER.log(Level.SEVERE, "MySQL JDBC Driver not found.", e);
            return null;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to connect to the database: " + e.getMessage(), e);
            return null;
        }
    }

    private static void createTablesIfNotExist(Connection conn) throws SQLException {
        String createUsersTable = """
            CREATE TABLE IF NOT EXISTS users (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(100) NOT NULL,
                student_id VARCHAR(20) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                department VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """;

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(createUsersTable);
            LOGGER.log(Level.INFO, "Database tables verified/created successfully.");
        }
    }
}
