<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.shape.Circle?>
<?import javafx.geometry.Insets?>

<BorderPane fx:id="dashboardContainer" stylesheets="@dashboard-new.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1"
          fx:controller="com.example.uniconnect.DashboardController" styleClass="root" prefWidth="800" prefHeight="600">

    <!-- Top Header -->
    <top>
        <HBox styleClass="header" spacing="10">
            <Label fx:id="greetingLabel" text="Good morning, maine uddin shanto" styleClass="greeting"/>
            <Region HBox.hgrow="ALWAYS"/>
            <HBox styleClass="notification" spacing="10">
                <Button styleClass="icon-button" text="📧"/>
                <Button styleClass="icon-button" text="🔔"/>
                <ImageView fx:id="profileImageView" fitHeight="40" fitWidth="40" styleClass="profile-img">
                    <Image url="@images/uniconnect.png"/>
                </ImageView>
            </HBox>
        </HBox>
    </top>

    <!-- Left Sidebar -->
    <left>
        <VBox styleClass="sidebar" prefWidth="200">
            <!-- Logo -->
            <VBox styleClass="logo-section">
                <Label text="Uniconnect" styleClass="logo-text"/>
            </VBox>

            <!-- Menu Items -->
            <VBox styleClass="menu-items" spacing="5">
                <HBox styleClass="sidebar-item">
                    <Label text="📷" styleClass="sidebar-icon"/>
                    <Label text="Profile" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item active">
                    <Label text="🏠" styleClass="sidebar-icon"/>
                    <Label text="Home" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item">
                    <Label text="👥" styleClass="sidebar-icon"/>
                    <Label text="Study Group" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item">
                    <Label text="📝" styleClass="sidebar-icon"/>
                    <Label text="Notes" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item">
                    <Label text="🔧" styleClass="sidebar-icon"/>
                    <Label text="Academic Tools" styleClass="sidebar-text"/>
                </HBox>
            </VBox>

            <Region VBox.vgrow="ALWAYS"/>

            <!-- Settings Menu -->
            <VBox styleClass="menu-settings" spacing="5">
                <HBox styleClass="sidebar-item">
                    <Label text="⚙️" styleClass="sidebar-icon"/>
                    <Label text="Settings" styleClass="sidebar-text"/>
                </HBox>
            </VBox>
        </VBox>
    </left>

    <!-- Center Content -->
    <center>
        <HBox spacing="0">
            <padding>
                <Insets top="20" right="0" bottom="20" left="20"/>
            </padding>

            <!-- Main Content Area -->
            <VBox styleClass="main-content" spacing="20" prefWidth="450" HBox.hgrow="ALWAYS">
                <!-- Post Input Area -->
                <VBox styleClass="post-input-container" spacing="15">
                    <HBox styleClass="post-input-area" spacing="10">
                        <Circle radius="20" styleClass="profile-circle"/>
                        <TextArea fx:id="postTextArea" promptText="What's on your mind?" styleClass="post-textarea" prefHeight="40" HBox.hgrow="ALWAYS"/>
                    </HBox>
                    <HBox styleClass="post-actions" spacing="10">
                        <ComboBox fx:id="categoryComboBox" promptText="Select Category" styleClass="category-dropdown" prefWidth="120"/>
                        <Button styleClass="photo-button" text="📷 Photo"/>
                        <Region HBox.hgrow="ALWAYS"/>
                        <Button fx:id="postButton" styleClass="post-button" text="Post" onAction="#handlePost"/>
                    </HBox>
                </VBox>

                <!-- Recent Posts Section -->
                <VBox spacing="15">
                    <Label text="Recent Posts" styleClass="section-title"/>

                    <VBox spacing="10">
                        <!-- Grace's Post -->
                        <VBox styleClass="post" spacing="15">
                            <HBox spacing="10">
                                <Circle radius="20" styleClass="profile-circle"/>
                                <VBox spacing="5" HBox.hgrow="ALWAYS">
                                    <HBox spacing="10">
                                        <Label text="Grace" styleClass="username"/>
                                        <Region HBox.hgrow="ALWAYS"/>
                                        <Button text="Academic" styleClass="category-tag"/>
                                    </HBox>
                                    <Label text="2 hours ago" styleClass="timestamp"/>
                                </VBox>
                            </HBox>
                            <Label text="Looking for CS101 notes for this trimester. Can anyone share?" wrapText="true" styleClass="post-text"/>
                            <HBox spacing="10" styleClass="post-buttons">
                                <Button text="👍 Like" styleClass="like-button"/>
                                <Button text="💬 Comment" styleClass="comment-button"/>
                            </HBox>
                        </VBox>

                    </VBox>
                </VBox>
            </VBox>

            <!-- Right Side: Calendar and Class Routine -->
            <VBox styleClass="right-sidebar" spacing="20" prefWidth="280">
                <padding>
                    <Insets top="0" right="20" bottom="0" left="20"/>
                </padding>

                <!-- Date and Calendar Status -->
                <VBox styleClass="calendar-section" spacing="10">
                    <Label fx:id="dateLabel" text="Friday, 25 April, 2025" styleClass="date-label"/>
                    <VBox spacing="5">
                        <Label text="Calendar Status" styleClass="calendar-header"/>
                        <Label text="In progress" styleClass="calendar-status"/>
                    </VBox>
                </VBox>

                <!-- Class Routine Section -->
                <VBox styleClass="routine-section" spacing="15">
                    <Label text="Your Class Routine" styleClass="routine-title"/>
                    <VBox styleClass="class-container" spacing="0">
                        <!-- Mathematics Class -->
                        <VBox styleClass="class-item">
                            <Label text="Mathematics" styleClass="class-subject"/>
                            <HBox spacing="5" styleClass="class-details">
                                <Label text="🕒" styleClass="time-icon"/>
                                <Label text="9:00 AM - 10:30 AM" styleClass="class-time"/>
                            </HBox>
                            <HBox spacing="10" styleClass="class-info">
                                <Label text="Section A" styleClass="section-tag"/>
                                <Label text="Room 101" styleClass="room-tag"/>
                            </HBox>
                        </VBox>
                    </VBox>
                </VBox>
            </VBox>
        </HBox>
    </center>
</BorderPane>