<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.shape.Circle?>
<?import javafx.geometry.Insets?>

<BorderPane fx:id="dashboardContainer" stylesheets="@dashboard-new.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1"
          fx:controller="com.example.uniconnect.DashboardController" styleClass="root" prefWidth="800" prefHeight="600">

    <!-- Top Header -->
    <top>
        <HBox styleClass="header">
            <HBox styleClass="header-item1">
                <VBox styleClass="search-bar">
                    <Label fx:id="greetingLabel" text="Good morning, Tommy" styleClass="greeting"/>
                </VBox>
                <Region HBox.hgrow="ALWAYS"/>
                <HBox styleClass="notification">
                    <Button styleClass="nav-icon" text="📧"/>
                    <Button styleClass="nav-icon" text="🔔"/>
                    <ImageView fx:id="profileImageView" fitHeight="45" fitWidth="45" styleClass="profile-img">
                        <Image url="@images/uniconnect.png"/>
                    </ImageView>
                </HBox>
            </HBox>
        </HBox>
    </top>

    <!-- Left Sidebar -->
    <left>
        <VBox styleClass="sidebar" prefWidth="200">
            <!-- Logo -->
            <VBox styleClass="menu">
                <ImageView fitHeight="80" fitWidth="120" preserveRatio="true">
                    <Image url="@images/uniconnect.png"/>
                </ImageView>
            </VBox>

            <!-- Menu Items -->
            <VBox styleClass="menu-general">
                <Hyperlink styleClass="sidebar-item" text="👤 Profile"/>
                <Hyperlink styleClass="sidebar-item active" text="🏠 Home"/>
                <Hyperlink styleClass="sidebar-item" text="👥 Study Group"/>
                <Hyperlink styleClass="sidebar-item" text="📝 Notes"/>
                <Hyperlink styleClass="sidebar-item" text="🔧 Academic Tools"/>
            </VBox>

            <Region VBox.vgrow="ALWAYS"/>

            <!-- Settings Menu -->
            <VBox styleClass="menu-settings">
                <Hyperlink styleClass="sidebar-item" text="⚙️ Settings"/>
                <Hyperlink styleClass="sidebar-item" text="🚪 Logout" onAction="#handleLogout"/>
            </VBox>
        </VBox>
    </left>

    <!-- Center Content -->
    <center>
        <VBox styleClass="main-content">
            <padding>
                <Insets top="20" right="20" bottom="20" left="20"/>
            </padding>
            <!-- Post Input Area -->
            <HBox styleClass="post-input" spacing="10">
                <Circle radius="20" styleClass="profile-circle"/>
                <TextArea fx:id="postTextArea" promptText="What's on your mind?" styleClass="post-textarea" HBox.hgrow="ALWAYS"/>
                <ComboBox fx:id="categoryComboBox" promptText="Select Category" styleClass="category-dropdown" prefWidth="150"/>
                <Button styleClass="photo-btn" text="📷"/>
                <Button fx:id="postButton" styleClass="post-button" text="Post" onAction="#handlePost"/>
            </HBox>

            <!-- Recent Posts Section -->
            <VBox styleClass="posts-section" spacing="20">
                <VBox styleClass="post" spacing="10">
                    <HBox spacing="10">
                        <Circle radius="20" styleClass="profile-circle"/>
                        <VBox HBox.hgrow="ALWAYS">
                            <Label text="Grace" styleClass="username"/>
                            <Label text="2 hours ago" styleClass="timestamp"/>
                            <Label text="Looking for CS101 notes for this trimester. Can anyone share?" wrapText="true" styleClass="post-text"/>
                            <Separator/>
                            <HBox spacing="10">
                                <Button text="👍 Like" styleClass="like-button"/>
                                <Button text="💬 Comment" styleClass="comment-button"/>
                            </HBox>
                        </VBox>
                    </HBox>
                </VBox>

                <VBox styleClass="post" spacing="10">
                    <HBox spacing="10">
                        <Circle radius="20" styleClass="profile-circle"/>
                        <VBox HBox.hgrow="ALWAYS">
                            <Label text="Arthur" styleClass="username"/>
                            <Label text="Yesterday" styleClass="timestamp"/>
                            <Label text="Selling my Statistics textbook. Barely used, perfect condition. $45 OBO." wrapText="true" styleClass="post-text"/>
                            <ImageView styleClass="post-image" fitWidth="400" preserveRatio="true">
                                <Image url="@images/Statistics.jpg"/>
                            </ImageView>
                            <Separator/>
                            <HBox spacing="10">
                                <Button text="👍 Like" styleClass="like-button"/>
                                <Button text="💬 Comment" styleClass="comment-button"/>
                            </HBox>
                        </VBox>
                    </HBox>
                </VBox>
            </VBox>

            <!-- Class Routine Section -->
            <VBox styleClass="routine" spacing="10">
                <Label text="Your Class Routine" styleClass="section-header"/>
                <VBox spacing="5">
                    <!-- Class 1 -->
                    <HBox styleClass="class-entry" spacing="10">
                        <Label text="Mathematics" styleClass="subject"/>
                        <Label text="9:00 AM - 10:30 AM" styleClass="time"/>
                        <Label text="Section A" styleClass="section"/>
                        <Label text="Room 101" styleClass="room"/>
                    </HBox>

                    <!-- Class 2 -->
                    <HBox styleClass="class-entry" spacing="10">
                        <Label text="Physics" styleClass="subject"/>
                        <Label text="11:00 AM - 12:30 PM" styleClass="time"/>
                        <Label text="Section B" styleClass="section"/>
                        <Label text="Room 203" styleClass="room"/>
                    </HBox>

                    <!-- Class 3 -->
                    <HBox styleClass="class-entry" spacing="10">
                        <Label text="Chemistry" styleClass="subject"/>
                        <Label text="2:00 PM - 3:30 PM" styleClass="time"/>
                        <Label text="Section A" styleClass="section"/>
                        <Label text="Room 305" styleClass="room"/>
                    </HBox>

                    <!-- Class 4 -->
                    <HBox styleClass="class-entry" spacing="10">
                        <Label text="English" styleClass="subject"/>
                        <Label text="4:00 PM - 5:30 PM" styleClass="time"/>
                        <Label text="Section C" styleClass="section"/>
                        <Label text="Room 112" styleClass="room"/>
                    </HBox>
                </VBox>
            </VBox>
        </VBox>
    </center>
</BorderPane>