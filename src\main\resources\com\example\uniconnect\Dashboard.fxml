<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.image.Image?>
<?import javafx.geometry.Insets?>

<GridPane fx:id="dashboardContainer" stylesheets="@dashboard.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1"
          fx:controller="com.example.uniconnect.DashboardController" styleClass="container">

    <columnConstraints>
        <ColumnConstraints percentWidth="20.0" />
        <ColumnConstraints percentWidth="80.0" />
    </columnConstraints>
    <rowConstraints>
        <RowConstraints prefHeight="80.0" />
        <RowConstraints vgrow="ALWAYS" />
    </rowConstraints>

    <!-- Header -->
    <HBox styleClass="header" GridPane.columnSpan="2" GridPane.columnIndex="0" GridPane.rowIndex="0">
        <HBox styleClass="header-item1">
            <VBox styleClass="search-bar">
                <Label fx:id="welcomeLabel" text="Good morning, " styleClass="welcome-text"/>
                <Label fx:id="userNameLabel" styleClass="user-name"/>
            </VBox>
            <Region HBox.hgrow="ALWAYS"/>
            <HBox styleClass="notification">
                <Button styleClass="nav-icon" text="📧"/>
                <Button styleClass="nav-icon" text="🔔"/>
                <ImageView fx:id="profileImageView" fitHeight="45" fitWidth="45" styleClass="profile-img">
                    <Image url="@images/uniconnect.png"/>
                </ImageView>
            </HBox>
        </HBox>
    </HBox>

    <!-- Sidebar -->
    <VBox styleClass="sidebar" GridPane.rowIndex="0" GridPane.columnIndex="0" GridPane.rowSpan="2">
        <!-- Logo -->
        <VBox styleClass="menu">
            <ImageView fitHeight="80" fitWidth="120" preserveRatio="true">
                <Image url="@images/uniconnect.png"/>
            </ImageView>
        </VBox>

        <!-- Menu Items -->
        <VBox styleClass="menu-general">
            <Button styleClass="sidebar-item" text="👤 Profile"/>
            <Button styleClass="sidebar-item active" text="🏠 Home"/>
            <Button styleClass="sidebar-item" text="👥 Study Group"/>
            <Button styleClass="sidebar-item" text="📝 Notes"/>
            <Button styleClass="sidebar-item" text="🔧 Academic Tools"/>
        </VBox>

        <Region VBox.vgrow="ALWAYS"/>

        <!-- Settings Menu -->
        <VBox styleClass="menu-settings">
            <Button styleClass="sidebar-item" text="⚙️ Settings"/>
            <Button styleClass="sidebar-item" text="🚪 Logout" onAction="#handleLogout"/>
        </VBox>
    </VBox>

    <!-- Main Content -->
    <VBox styleClass="content" GridPane.columnIndex="1" GridPane.rowIndex="1">
        <HBox styleClass="content-main">
            <!-- Left Side: Posts Feed -->
            <VBox styleClass="content-left" HBox.hgrow="ALWAYS">
                <!-- Create Post -->
                <VBox styleClass="create-post">
                    <HBox styleClass="post-input">
                        <VBox styleClass="create-post-avatar">
                            <ImageView fitHeight="40" fitWidth="40" preserveRatio="true">
                                <Image url="@images/uniconnect.png"/>
                            </ImageView>
                        </VBox>
                        <VBox styleClass="post-input-content" HBox.hgrow="ALWAYS">
                            <TextArea fx:id="postTextArea" promptText="What's on your mind?" styleClass="post-textarea"/>
                            <HBox styleClass="post-options">
                                <ComboBox fx:id="categoryComboBox" promptText="Select Category" styleClass="category-select"/>
                                <Button styleClass="photo-upload" text="📷 Photo"/>
                                <Region HBox.hgrow="ALWAYS"/>
                                <Button fx:id="postButton" styleClass="post-button" text="Post" onAction="#handlePost"/>
                            </HBox>
                        </VBox>
                    </HBox>
                </VBox>

                <!-- Posts Feed -->
                <VBox styleClass="posts-feed">
                    <Label text="Recent Posts" styleClass="feed-header"/>

                    <!-- Post 1 -->
                    <VBox styleClass="post">
                        <HBox styleClass="post-header">
                            <VBox styleClass="user-avatar">
                                <ImageView fitHeight="40" fitWidth="40" preserveRatio="true">
                                    <Image url="@images/uniconnect.png"/>
                                </ImageView>
                            </VBox>
                            <VBox styleClass="post-meta" HBox.hgrow="ALWAYS">
                                <HBox styleClass="meta-top">
                                    <Label styleClass="user-name" text="Grace"/>
                                    <Region HBox.hgrow="ALWAYS"/>
                                    <Label styleClass="category-tag" text="Academic"/>
                                </HBox>
                                <Label styleClass="timestamp" text="2 hours ago"/>
                                <VBox styleClass="post-content">
                                    <Label text="Looking for CS101 notes for this trimester. Can anyone share?" wrapText="true" styleClass="post-text"/>
                                </VBox>
                                <Separator styleClass="post-divider"/>
                                <HBox styleClass="post-actions">
                                    <Button styleClass="action-btn" text="👍 Like"/>
                                    <Button styleClass="action-btn" text="💬 Comment"/>
                                </HBox>
                            </VBox>
                        </HBox>
                    </VBox>

                    <!-- Post 2 -->
                    <VBox styleClass="post">
                        <HBox styleClass="post-header">
                            <VBox styleClass="user-avatar">
                                <ImageView fitHeight="40" fitWidth="40" preserveRatio="true">
                                    <Image url="@images/uniconnect.png"/>
                                </ImageView>
                            </VBox>
                            <VBox styleClass="post-meta" HBox.hgrow="ALWAYS">
                                <HBox styleClass="meta-top">
                                    <Label styleClass="user-name" text="Arthur"/>
                                    <Region HBox.hgrow="ALWAYS"/>
                                    <Label styleClass="category-tag" text="Buy &amp; Sell"/>
                                </HBox>
                                <Label styleClass="timestamp" text="Yesterday"/>
                                <VBox styleClass="post-content">
                                    <Label text="Selling my Statistics textbook. Barely used, perfect condition. $45 OBO." wrapText="true" styleClass="post-text"/>
                                    <ImageView styleClass="post-image" fitWidth="400" preserveRatio="true">
                                        <Image url="@images/Statistics.jpg"/>
                                    </ImageView>
                                </VBox>
                                <Separator styleClass="post-divider"/>
                                <HBox styleClass="post-actions">
                                    <Button styleClass="action-btn" text="👍 Like"/>
                                    <Button styleClass="action-btn" text="💬 Comment"/>
                                </HBox>
                            </VBox>
                        </HBox>
                    </VBox>
                </VBox>
            </VBox>

            <!-- Right Side: Friday and Routine -->
            <VBox styleClass="right-side-container" prefWidth="350">
                <!-- Friday Grid -->
                <VBox styleClass="friday-grid">
                    <Label fx:id="dateLabel" text="Friday, 25 April, 2025" styleClass="date-label"/>
                    <VBox styleClass="calendar-grid">
                        <Label text="Calendar Status" styleClass="calendar-status"/>
                        <Label styleClass="status-badge" text="In progress"/>
                    </VBox>
                </VBox>

                <!-- Class Routine -->
                <VBox styleClass="class-routine">
                    <Label styleClass="header-label" text="Your Class Routine"/>
                    <ScrollPane styleClass="class-list" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED">
                        <VBox>
                            <!-- Class 1 -->
                            <VBox styleClass="class-item subject-math">
                                <VBox styleClass="class-details">
                                    <Label styleClass="subject-label" text="Mathematics"/>
                                    <Label styleClass="class-time" text="🕒 9:00 AM - 10:30 AM"/>
                                    <HBox styleClass="class-location">
                                        <Label styleClass="section-tag" text="Section A"/>
                                        <Label styleClass="room-tag" text="Room 101"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- Class 2 -->
                            <VBox styleClass="class-item subject-physics">
                                <VBox styleClass="class-details">
                                    <Label styleClass="subject-label" text="Physics"/>
                                    <Label styleClass="class-time" text="🕒 11:00 AM - 12:30 PM"/>
                                    <HBox styleClass="class-location">
                                        <Label styleClass="section-tag" text="Section B"/>
                                        <Label styleClass="room-tag" text="Room 203"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- Class 3 -->
                            <VBox styleClass="class-item subject-chemistry">
                                <VBox styleClass="class-details">
                                    <Label styleClass="subject-label" text="Chemistry"/>
                                    <Label styleClass="class-time" text="🕒 2:00 PM - 3:30 PM"/>
                                    <HBox styleClass="class-location">
                                        <Label styleClass="section-tag" text="Section A"/>
                                        <Label styleClass="room-tag" text="Room 305"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- Class 4 -->
                            <VBox styleClass="class-item subject-english">
                                <VBox styleClass="class-details">
                                    <Label styleClass="subject-label" text="English"/>
                                    <Label styleClass="class-time" text="🕒 4:00 PM - 5:30 PM"/>
                                    <HBox styleClass="class-location">
                                        <Label styleClass="section-tag" text="Section C"/>
                                        <Label styleClass="room-tag" text="Room 112"/>
                                    </HBox>
                                </VBox>
                            </VBox>
                        </VBox>
                    </ScrollPane>
                </VBox>
            </VBox>
        </HBox>
    </VBox>
</GridPane>