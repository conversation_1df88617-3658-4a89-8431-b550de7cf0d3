<?php
require 'db_connect.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get POST data
    $full_name   = $_POST['full_name'];
    $student_id  = $_POST['student_id'];
    $email       = $_POST['email'];
    $password    = $_POST['password'];
    $confirmPass = $_POST['confirm_password'];
    $department  = $_POST['department'];

    // Basic validation
    if ($password !== $confirmPass) {
        echo "<script>alert('Passwords do not match.'); window.location.href='index.php';</script>";
        exit;
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo "<script>alert('Invalid email format.'); window.location.href='index.php';</script>";
        exit;
    }

    // Hash the password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    try {
        // Prepare and execute insert
        $stmt = $pdo->prepare("INSERT INTO users (full_name, student_id, email, password, department) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$full_name, $student_id, $email, $hashed_password, $department]);

        // Redirect to login page with success message
        echo "<script>alert('Signup successful! Please login.'); window.location.href='index.php';</script>";
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) {
            echo "<script>alert('Student ID or email already exists.'); window.location.href='index.php';</script>";
        } else {
            echo "<script>alert('Error: " . $e->getMessage() . "'); window.location.href='index.php';</script>";
        }
    }
} else {
    // If someone tries to access this page directly, redirect to index
    header("Location: index.php");
    exit;
}
?>
