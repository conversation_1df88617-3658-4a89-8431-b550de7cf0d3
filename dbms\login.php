<?php
require 'db_connect.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $student_id = $_POST['student_id'];
    $password   = $_POST['password'];

    try {
        // Find the user
        $stmt = $pdo->prepare("SELECT * FROM users WHERE student_id = ?");
        $stmt->execute([$student_id]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            // Start session and store user info
            session_start();
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['student_id'] = $user['student_id'];
            $_SESSION['full_name'] = $user['full_name'];

            // Redirect to dashboard
            header("Location: dashboard.php");
            exit;
        } else {
            echo "<script>alert('Invalid student ID or password.'); window.location.href='index.php';</script>";
        }
    } catch (PDOException $e) {
        echo "<script>alert('Error: " . $e->getMessage() . "'); window.location.href='index.php';</script>";
    }
} else {
    // If someone tries to access this page directly, redirect to index
    header("Location: index.php");
    exit;
}
?>
