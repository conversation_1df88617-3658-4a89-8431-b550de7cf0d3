<?php
// Database connection settings
$host_options = ['localhost', '127.0.0.1', '::1'];
$username = 'root';
$password = '';
$dbname = 'uniconnect';
$connected = false;
$last_error = '';

// Try different connection methods
foreach ($host_options as $host) {
    try {
        // First try connecting without specifying a database
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Check if database exists, create if it doesn't
        $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
        if ($stmt->rowCount() == 0) {
            // Database doesn't exist, create it
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname`");
        }

        // Now connect with the database
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Check if users table exists, create if it doesn't
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, create it
            $sql = "CREATE TABLE IF NOT EXISTS users (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(100) NOT NULL,
                student_id VARCHAR(20) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                department VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $pdo->exec($sql);
        }

        $connected = true;
        break; // Connection successful, exit the loop
    } catch(PDOException $e) {
        $last_error = $e->getMessage();
        // Continue to the next host option
    }
}

if (!$connected) {
    // All connection attempts failed
    echo "<div style='background-color: #ffdddd; color: #990000; padding: 10px; margin: 10px; border: 1px solid #990000;'>";
    echo "<h3>Database Connection Error</h3>";
    echo "<p>Could not connect to the MySQL server. Please check that:</p>";
    echo "<ol>";
    echo "<li>XAMPP/MySQL service is running</li>";
    echo "<li>The database credentials are correct</li>";
    echo "</ol>";
    echo "<p>Error details: " . htmlspecialchars($last_error) . "</p>";
    echo "</div>";
    die();
}
?>