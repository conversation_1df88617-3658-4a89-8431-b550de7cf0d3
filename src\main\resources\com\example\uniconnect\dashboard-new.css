/* Global Styles - matching DBMS project exactly */
.root {
    -fx-font-family: "Arial", "sans-serif";
    -fx-font-size: 14px;
    -fx-background-color: #F5F5F5;
}

/* Sidebar Styles */
.sidebar {
    -fx-background-color: #1A237E;
    -fx-pref-width: 200px;
    -fx-spacing: 10;
    -fx-padding: 10;
}

.sidebar .active {
    -fx-background-color: #0D47A1;
}

.sidebar .sidebar-item {
    -fx-padding: 10;
    -fx-text-fill: #FFFFFF;
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-cursor: hand;
}

.sidebar .sidebar-item:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
}

/* Header Styles */
.header {
    -fx-background-color: #E0E0E0;
    -fx-padding: 10;
    -fx-spacing: 10;
}

.greeting {
    -fx-font-weight: bold;
    -fx-font-size: 18px;
}

.notification {
    -fx-spacing: 10;
    -fx-alignment: center-right;
}

.nav-icon {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 20px;
    -fx-cursor: hand;
    -fx-padding: 5;
}

.profile-img {
    -fx-background-radius: 50%;
}

/* Main Content Styles */
.main-content {
    -fx-background-color: #F5F5F5;
    -fx-spacing: 20;
}

/* Post Input Area Styles */
.post-input {
    -fx-background-color: #E3F2FD;
    -fx-padding: 10;
    -fx-background-radius: 5;
}

.post-textarea {
    -fx-pref-height: 100;
    -fx-background-color: #E3F2FD;
    -fx-background-radius: 8;
    -fx-padding: 10;
    -fx-font-size: 14px;
}

.post-button {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 5 10;
    -fx-background-radius: 5;
}

.category-dropdown {
    -fx-background-color: #F0F2F5;
    -fx-background-radius: 5;
}

.profile-circle {
    -fx-fill: #B0BEC5;
    -fx-stroke: #757575;
    -fx-stroke-width: 2;
}

/* Post Styles */
.post {
    -fx-background-color: #FFFFFF;
    -fx-padding: 10;
    -fx-background-radius: 5;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

.username {
    -fx-font-weight: bold;
}

.timestamp {
    -fx-text-fill: #757575;
}

.like-button {
    -fx-background-color: #1976D2;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 5 10;
    -fx-background-radius: 5;
}

.comment-button {
    -fx-background-color: #B0BEC5;
    -fx-text-fill: #000000;
    -fx-padding: 5 10;
    -fx-background-radius: 5;
}

/* Class Routine Styles */
.routine {
    -fx-background-color: #E0F7FA;
    -fx-padding: 10;
    -fx-background-radius: 5;
}

.section-header {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #1A237E;
}

.class-entry {
    -fx-padding: 5;
}

.subject {
    -fx-font-weight: bold;
    -fx-min-width: 100;
}

.time {
    -fx-background-color: #C8E6C9;
    -fx-padding: 5;
    -fx-background-radius: 3;
    -fx-min-width: 150;
}

.section {
    -fx-background-color: #FFCCBC;
    -fx-padding: 5;
    -fx-background-radius: 3;
    -fx-min-width: 80;
}

.room {
    -fx-background-color: #FFCCBC;
    -fx-padding: 5;
    -fx-background-radius: 3;
    -fx-min-width: 80;
}
