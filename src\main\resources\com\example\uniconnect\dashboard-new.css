/* Global Styles - matching reference design exactly */
.root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-background-color: #F8F9FA;
}

/* Sidebar Styles - Black background as in reference */
.sidebar {
    -fx-background-color: #000000;
    -fx-pref-width: 200px;
    -fx-spacing: 0;
    -fx-padding: 20 0;
}

.logo-section {
    -fx-padding: 0 20 30 20;
}

.logo-text {
    -fx-text-fill: #FFFFFF;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
}

.menu-items {
    -fx-padding: 0 0 20 0;
}

.sidebar-item {
    -fx-padding: 12 20;
    -fx-spacing: 12;
    -fx-alignment: center-left;
    -fx-cursor: hand;
}

.sidebar-item:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-item.active {
    -fx-background-color: rgba(255, 255, 255, 0.15);
}

.sidebar-icon {
    -fx-text-fill: #FFFFFF;
    -fx-font-size: 16px;
    -fx-min-width: 20px;
}

.sidebar-text {
    -fx-text-fill: #FFFFFF;
    -fx-font-size: 14px;
}

/* Header Styles */
.header {
    -fx-background-color: #FFFFFF;
    -fx-padding: 15 20;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.greeting {
    -fx-font-weight: normal;
    -fx-font-size: 16px;
    -fx-text-fill: #333333;
}

.notification {
    -fx-spacing: 15;
    -fx-alignment: center-right;
}

.icon-button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 18px;
    -fx-cursor: hand;
    -fx-padding: 8;
    -fx-background-radius: 4;
}

.icon-button:hover {
    -fx-background-color: #F0F0F0;
}

.profile-img {
    -fx-background-radius: 50%;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);
}

/* Main Content Styles */
.main-content {
    -fx-background-color: transparent;
    -fx-spacing: 20;
}

/* Post Input Area Styles */
.post-input-container {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.post-input-area {
    -fx-alignment: center-left;
}

.post-textarea {
    -fx-pref-height: 40;
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 8;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: #999999;
}

.post-actions {
    -fx-alignment: center-left;
}

.photo-button {
    -fx-background-color: #F0F0F0;
    -fx-text-fill: #666666;
    -fx-padding: 8 12;
    -fx-background-radius: 6;
    -fx-border-color: #DDDDDD;
    -fx-border-radius: 6;
    -fx-font-size: 13px;
}

.photo-button:hover {
    -fx-background-color: #E8E8E8;
}

.post-button {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 8 16;
    -fx-background-radius: 6;
    -fx-font-size: 13px;
    -fx-font-weight: bold;
}

.post-button:hover {
    -fx-background-color: #333333;
}

.category-dropdown {
    -fx-background-color: #F8F9FA;
    -fx-background-radius: 6;
    -fx-border-color: #DDDDDD;
    -fx-border-radius: 6;
    -fx-padding: 8;
    -fx-font-size: 13px;
}

.profile-circle {
    -fx-fill: #E0E0E0;
    -fx-stroke: #CCCCCC;
    -fx-stroke-width: 1;
}

/* Post Styles */
.section-title {
    -fx-font-weight: bold;
    -fx-font-size: 18px;
    -fx-text-fill: #333333;
    -fx-padding: 0 0 10 0;
}

.post {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.username {
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-text-fill: #333333;
}

.timestamp {
    -fx-text-fill: #999999;
    -fx-font-size: 12px;
}

.post-text {
    -fx-text-fill: #333333;
    -fx-font-size: 14px;
    -fx-line-spacing: 2;
}

.post-buttons {
    -fx-alignment: center-left;
}

.like-button {
    -fx-background-color: #1877F2;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 8 16;
    -fx-background-radius: 6;
    -fx-font-size: 13px;
}

.like-button:hover {
    -fx-background-color: #166FE5;
}

.comment-button {
    -fx-background-color: #E4E6EA;
    -fx-text-fill: #333333;
    -fx-padding: 8 16;
    -fx-background-radius: 6;
    -fx-font-size: 13px;
}

.comment-button:hover {
    -fx-background-color: #D8DADF;
}

.category-tag {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 4 8;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
}

/* Right Sidebar Styles */
.right-sidebar {
    -fx-background-color: transparent;
}

.calendar-section {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.date-label {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.calendar-header {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.calendar-status {
    -fx-font-size: 14px;
    -fx-text-fill: #1877F2;
    -fx-font-weight: normal;
}

.routine-section {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.routine-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.class-container {
    -fx-background-color: transparent;
}

.class-item {
    -fx-background-color: #F8F9FA;
    -fx-padding: 15;
    -fx-background-radius: 8;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-spacing: 8;
}

.class-subject {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.class-details {
    -fx-alignment: center-left;
}

.time-icon {
    -fx-font-size: 14px;
}

.class-time {
    -fx-font-size: 14px;
    -fx-text-fill: #666666;
}

.class-info {
    -fx-alignment: center-left;
}

.section-tag {
    -fx-background-color: #E8F5E8;
    -fx-text-fill: #2E7D32;
    -fx-padding: 4 8;
    -fx-background-radius: 4;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.room-tag {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 4 8;
    -fx-background-radius: 4;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}
